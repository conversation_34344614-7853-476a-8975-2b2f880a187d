import 'package:flutter_test/flutter_test.dart';
import '../lib/core/api/api_exception.dart';
import '../lib/core/api/api_config.dart';
import '../lib/services/payment/cashfree_service.dart';

void main() {
  group('Cashfree Response Handling Tests', () {
    group('OrderId Extraction Logic', () {
      test('should extract orderId from transactionId field', () {
        // Arrange
        final responseData = {
          'transactionId': 'CF_ORDER_123456',
          'code': 'SUCCESS',
          'response': {'status': 'success'},
        };

        // Act
        String? orderId = responseData['transactionId']?.toString();

        // Assert
        expect(orderId, equals('CF_ORDER_123456'));
      });

      test('should extract orderId from orderId field when transactionId is not available', () {
        // Arrange
        final responseData = {
          'orderId': 'CF_ORDER_789012',
          'code': 'SUCCESS',
        };

        // Act
        String? orderId = responseData['transactionId']?.toString() ??
            responseData['orderId']?.toString();

        // Assert
        expect(orderId, equals('CF_ORDER_789012'));
      });

      test('should extract orderId from order_id field as fallback', () {
        // Arrange
        final responseData = {
          'order_id': 'CF_ORDER_345678',
          'status': 'completed',
        };

        // Act
        String? orderId = responseData['transactionId']?.toString() ??
            responseData['orderId']?.toString() ??
            responseData['order_id']?.toString();

        // Assert
        expect(orderId, equals('CF_ORDER_345678'));
      });

      test('should extract orderId from nested response.orderId field', () {
        // Arrange
        final responseData = {
          'response': {
            'orderId': 'CF_ORDER_NESTED_123',
            'status': 'success'
          },
          'code': 'SUCCESS',
        };

        // Act
        String? orderId = responseData['transactionId']?.toString() ??
            responseData['orderId']?.toString() ??
            responseData['order_id']?.toString() ??
            (responseData['response'] as Map<String, dynamic>?)?['orderId']?.toString();

        // Assert
        expect(orderId, equals('CF_ORDER_NESTED_123'));
      });

      test('should extract orderId from nested data.orderId field', () {
        // Arrange
        final responseData = {
          'data': {
            'orderId': 'CF_ORDER_DATA_456',
            'amount': 250.0
          },
          'status': 'pending',
        };

        // Act
        String? orderId = responseData['transactionId']?.toString() ??
            responseData['orderId']?.toString() ??
            responseData['order_id']?.toString() ??
            (responseData['response'] as Map<String, dynamic>?)?['orderId']?.toString() ??
            (responseData['data'] as Map<String, dynamic>?)?['orderId']?.toString();

        // Assert
        expect(orderId, equals('CF_ORDER_DATA_456'));
      });

      test('should return null when no orderId is found', () {
        // Arrange
        final responseData = {
          'code': 'SUCCESS',
          'status': 'completed',
          'amount': 100.0,
        };

        // Act
        String? orderId = responseData['transactionId']?.toString() ??
            responseData['orderId']?.toString() ??
            responseData['order_id']?.toString() ??
            (responseData['response'] as Map<String, dynamic>?)?['orderId']?.toString() ??
            (responseData['data'] as Map<String, dynamic>?)?['orderId']?.toString();

        // Assert
        expect(orderId, isNull);
      });
    });

    group('CashfreePaymentResult', () {
      test('should create success result with correct type and message', () {
        // Arrange & Act
        final result = CashfreePaymentResult.success(
          'Payment completed successfully',
          {'orderId': 'CF_ORDER_123'}
        );

        // Assert
        expect(result.type, equals(CashfreePaymentResultType.success));
        expect(result.message, equals('Payment completed successfully'));
        expect(result.data?['orderId'], equals('CF_ORDER_123'));
      });

      test('should create failed result with correct type and message', () {
        // Arrange & Act
        final result = CashfreePaymentResult.failed(
          'Payment failed due to insufficient funds',
          {'errorCode': 'INSUFFICIENT_FUNDS'}
        );

        // Assert
        expect(result.type, equals(CashfreePaymentResultType.failed));
        expect(result.message, equals('Payment failed due to insufficient funds'));
        expect(result.data?['errorCode'], equals('INSUFFICIENT_FUNDS'));
      });

      test('should create cancelled result with default message', () {
        // Arrange & Act
        final result = CashfreePaymentResult.cancelled();

        // Assert
        expect(result.type, equals(CashfreePaymentResultType.cancelled));
        expect(result.message, equals('Payment cancelled by user'));
      });

      test('should create timeout result with default message', () {
        // Arrange & Act
        final result = CashfreePaymentResult.timeout();

        // Assert
        expect(result.type, equals(CashfreePaymentResultType.timeout));
        expect(result.message, equals('Payment timed out'));
      });
    });

    group('Error Handling', () {
      test('should handle ApiException properly', () {
        // Arrange
        final apiException = ApiException(
          'Connection timed out. Please check your internet connection and try again.',
          code: 'CONNECTION_TIMEOUT',
        );

        // Act & Assert
        expect(apiException.message, contains('Connection timed out'));
        expect(apiException.code, equals('CONNECTION_TIMEOUT'));
      });

      test('should handle missing order ID error', () {
        // Arrange
        final expectedError = ApiException(
          'Order ID not found in payment response data.',
          code: 'MISSING_ORDER_ID',
        );

        // Act & Assert
        expect(expectedError.message, contains('Order ID not found'));
        expect(expectedError.code, equals('MISSING_ORDER_ID'));
      });

      test('should handle invalid response format error', () {
        // Arrange
        final expectedError = ApiException(
          'Invalid response format received for Cashfree payment response.',
          code: 'INVALID_RESPONSE_FORMAT',
        );

        // Act & Assert
        expect(expectedError.message, contains('Invalid response format'));
        expect(expectedError.code, equals('INVALID_RESPONSE_FORMAT'));
      });
    });

    group('Integration Tests', () {
      test('should process wallet screen payload correctly', () {
        // Arrange - Simulating wallet screen payload
        final walletPayload = {
          'transactionId': 'WALLET_TXN_123456789',
        };

        final expectedOrderId = 'WALLET_TXN_123456789';

        // Act & Assert
        expect(walletPayload['transactionId'], equals(expectedOrderId));
      });

      test('should process RFID page payload correctly', () {
        // Arrange - Simulating RFID page payload
        final rfidPayload = {
          'transactionId': 'RFID_ORDER_987654321',
        };

        final expectedOrderId = 'RFID_ORDER_987654321';

        // Act & Assert
        expect(rfidPayload['transactionId'], equals(expectedOrderId));
      });
    });

    group('API Configuration', () {
      test('should use correct Cashfree response endpoint', () {
        // Act & Assert
        expect(ApiConfig.cashfreeResponse, equals('/user/payment/response-cashfree'));
        expect(ApiConfig.apiUrl, contains('api2.eeil.online'));
      });

      test('should have consistent endpoint pattern with other gateways', () {
        // Act & Assert
        expect(ApiConfig.phonepeResponse, equals('/user/payment/response-phonepev2'));
        expect(ApiConfig.payuResponse, equals('/user/payment/response-payu'));
        expect(ApiConfig.cashfreeResponse, equals('/user/payment/response-cashfree'));
        
        // All should follow the same pattern
        expect(ApiConfig.phonepeResponse, startsWith('/user/payment/response-'));
        expect(ApiConfig.payuResponse, startsWith('/user/payment/response-'));
        expect(ApiConfig.cashfreeResponse, startsWith('/user/payment/response-'));
      });
    });
  });
}
