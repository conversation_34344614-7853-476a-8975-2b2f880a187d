import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ecoplug/screens/dashboard/google_map_widget.dart';
import 'package:ecoplug/services/performance_monitoring_service.dart';
import 'package:ecoplug/services/google_maps_performance_service.dart';
import 'package:ecoplug/services/working_clustering_service.dart';
import 'package:ecoplug/services/persistent_marker_service.dart';

/// Comprehensive performance tests for Google Maps optimization
/// Tests frame rate, memory usage, clustering efficiency, and cache performance
void main() {
  group('Google Maps Performance Tests', () {
    late ApplicationPerformanceMonitor performanceMonitor;
    late GoogleMapsPerformanceService mapsPerformanceService;
    late WorkingClusteringService clusteringService;
    late PersistentMarkerService markerService;

    setUp(() {
      performanceMonitor = ApplicationPerformanceMonitor();
      mapsPerformanceService = GoogleMapsPerformanceService();
      clusteringService = WorkingClusteringService();
      markerService = PersistentMarkerService();
    });

    tearDown(() {
      performanceMonitor.dispose();
      mapsPerformanceService.dispose();
    });

    testWidgets('Google Map Widget Performance Test', (WidgetTester tester) async {
      // Initialize performance monitoring
      performanceMonitor.initializeMonitoring();

      // Create test stations data
      final testStations = _generateTestStations(100);

      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: GoogleMapWidget(
                stations: testStations,
                initialLatitude: 28.6139,
                initialLongitude: 77.2090,
                initialZoom: 10.0,
                enableClustering: true,
                onStationSelected: (station) {
                  // Test callback
                },
              ),
            ),
          ),
        ),
      );

      // Wait for initial render
      await tester.pumpAndSettle(const Duration(seconds: 2));

      // Simulate user interactions
      await _simulateMapInteractions(tester);

      // Get performance metrics
      final analytics = performanceMonitor.getPerformanceAnalytics();

      // Performance assertions
      expect(analytics['averageFrameRate'], greaterThan(45.0),
          reason: 'Average frame rate should be above 45fps');
      expect(analytics['averageFrameTime'], lessThan(22.0),
          reason: 'Average frame time should be below 22ms');
      expect(performanceMonitor.isPerformanceOptimal, isTrue,
          reason: 'Performance should be optimal');

      print('📊 Performance Test Results:');
      print('   Average FPS: ${analytics['averageFrameRate']}');
      print('   Average Frame Time: ${analytics['averageFrameTime']}ms');
      print('   Total Frames: ${analytics['totalFrameCount']}');
    });

    test('Clustering Service Performance Test', () async {
      final testStations = _generateTestStations(500);
      final stopwatch = Stopwatch()..start();

      // Test clustering performance
      final markers = await clusteringService.generateClusteredMarkers(
        stations: testStations,
        zoomLevel: 10.0,
        onStationTap: (station) {},
        onClusterTap: (stations) {},
        createStationMarker: (station) async {
          return Marker(
            markerId: MarkerId(station['id'].toString()),
            position: LatLng(
              station['latitude'] as double,
              station['longitude'] as double,
            ),
          );
        },
      );

      stopwatch.stop();
      final clusteringTime = stopwatch.elapsedMilliseconds;

      // Performance assertions
      expect(clusteringTime, lessThan(2000),
          reason: 'Clustering should complete within 2 seconds');
      expect(markers.length, lessThan(testStations.length),
          reason: 'Clustering should reduce marker count');

      print('🔗 Clustering Performance:');
      print('   Input stations: ${testStations.length}');
      print('   Output markers: ${markers.length}');
      print('   Clustering time: ${clusteringTime}ms');
      print('   Reduction ratio: ${((testStations.length - markers.length) / testStations.length * 100).toStringAsFixed(1)}%');
    });

    test('Marker Service Cache Performance Test', () async {
      await markerService.initialize();
      final stopwatch = Stopwatch();

      // Test cache performance with multiple requests using actual station statuses
      final testRequests = [
        'Available',
        'In Use',
        'Unavailable',
        'Charging',
      ];

      // First request (cache miss)
      stopwatch.start();
      for (final type in testRequests) {
        await markerService.getMarkerDescriptorForStatus(type, focused: false, width: 48, height: 48);
      }
      stopwatch.stop();
      final firstRequestTime = stopwatch.elapsedMilliseconds;

      // Second request (cache hit)
      stopwatch.reset();
      stopwatch.start();
      for (final type in testRequests) {
        await markerService.getMarkerDescriptorForStatus(type, focused: false, width: 48, height: 48);
      }
      stopwatch.stop();
      final secondRequestTime = stopwatch.elapsedMilliseconds;

      // Performance assertions
      expect(secondRequestTime, lessThan(firstRequestTime * 0.5),
          reason: 'Cache hits should be significantly faster');
      expect(firstRequestTime, lessThan(5000),
          reason: 'Initial marker loading should complete within 5 seconds');

      print('🎯 Marker Cache Performance:');
      print('   First request (cache miss): ${firstRequestTime}ms');
      print('   Second request (cache hit): ${secondRequestTime}ms');
      print('   Cache efficiency: ${((firstRequestTime - secondRequestTime) / firstRequestTime * 100).toStringAsFixed(1)}% faster');
    });

    test('Memory Usage Test', () async {
      final initialMemory = _getCurrentMemoryUsage();
      
      // Create large dataset
      final largeStations = _generateTestStations(1000);
      
      // Process with clustering
      await clusteringService.generateClusteredMarkers(
        stations: largeStations,
        zoomLevel: 8.0,
        onStationTap: (station) {},
        onClusterTap: (stations) {},
        createStationMarker: (station) async {
          return Marker(
            markerId: MarkerId(station['id'].toString()),
            position: LatLng(
              station['latitude'] as double,
              station['longitude'] as double,
            ),
          );
        },
      );

      final finalMemory = _getCurrentMemoryUsage();
      final memoryIncrease = finalMemory - initialMemory;

      // Memory assertions
      expect(memoryIncrease, lessThan(50 * 1024 * 1024),
          reason: 'Memory increase should be less than 50MB');

      print('💾 Memory Usage Test:');
      print('   Initial memory: ${(initialMemory / 1024 / 1024).toStringAsFixed(1)}MB');
      print('   Final memory: ${(finalMemory / 1024 / 1024).toStringAsFixed(1)}MB');
      print('   Memory increase: ${(memoryIncrease / 1024 / 1024).toStringAsFixed(1)}MB');
    });
  });
}

/// Generate test station data for performance testing
List<Map<String, dynamic>> _generateTestStations(int count) {
  final stations = <Map<String, dynamic>>[];
  final random = DateTime.now().millisecondsSinceEpoch;
  
  for (int i = 0; i < count; i++) {
    stations.add({
      'id': 'test_station_$i',
      'uid': 'uid_$i',
      'name': 'Test Station $i',
      'latitude': 28.6139 + (i % 100) * 0.01 - 0.5,
      'longitude': 77.2090 + (i % 100) * 0.01 - 0.5,
      'status': ['available', 'occupied', 'offline'][i % 3],
      'distance': (i * 0.1) + 1.0,
    });
  }
  
  return stations;
}

/// Simulate user interactions with the map
Future<void> _simulateMapInteractions(WidgetTester tester) async {
  // Simulate tap gestures
  await tester.tap(find.byType(GoogleMapWidget));
  await tester.pumpAndSettle(const Duration(milliseconds: 500));

  // Simulate pan gestures
  await tester.drag(find.byType(GoogleMapWidget), const Offset(100, 100));
  await tester.pumpAndSettle(const Duration(milliseconds: 500));

  // Simulate zoom gestures
  await tester.timedDrag(
    find.byType(GoogleMapWidget),
    const Offset(0, -100),
    const Duration(milliseconds: 300),
  );
  await tester.pumpAndSettle(const Duration(milliseconds: 500));
}

/// Get current memory usage (simplified for testing)
int _getCurrentMemoryUsage() {
  // In a real implementation, this would use platform-specific memory APIs
  // For testing purposes, we'll return a mock value
  return DateTime.now().millisecondsSinceEpoch % 100000000;
}
