import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';
import '../../../../models/api_profile_model.dart';
import '../../../../models/rfid_order_model.dart';
import '../../../../core/api/api_service.dart';
import '../../../../core/api/api_exception.dart';

import '../../../../services/payment/phonepe_service.dart';
import '../../../../services/payment/payu_service.dart';
import '../../../../services/payment/cashfree_service.dart';
import 'package:flutter_cashfree_pg_sdk/utils/cfenums.dart';

class RfidOrderForm extends StatefulWidget {
  final ProfileData? profileData;

  const RfidOrderForm({super.key, this.profileData});

  @override
  State<RfidOrderForm> createState() => _RfidOrderFormState();
}

class _RfidOrderFormState extends State<RfidOrderForm> {
  final _formKey = GlobalKey<FormState>();

  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _stateController = TextEditingController();
  final TextEditingController _pincodeController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _gstController = TextEditingController();
  final TextEditingController _businessNameController = TextEditingController();

  bool _isLoading = false;

  // Payment flow state management
  bool _isPaymentInProgress = false;
  double? _pendingPaymentAmount;
  String? _currentTransactionId;

  // API service instance
  final ApiService _apiService = ApiService();

  // Indian states list for dropdown
  final List<String> _indianStates = [
    'Andhra Pradesh',
    'Arunachal Pradesh',
    'Assam',
    'Bihar',
    'Chhattisgarh',
    'Goa',
    'Gujarat',
    'Haryana',
    'Himachal Pradesh',
    'Jharkhand',
    'Karnataka',
    'Kerala',
    'Madhya Pradesh',
    'Maharashtra',
    'Manipur',
    'Meghalaya',
    'Mizoram',
    'Nagaland',
    'Odisha',
    'Punjab',
    'Rajasthan',
    'Sikkim',
    'Tamil Nadu',
    'Telangana',
    'Tripura',
    'Uttar Pradesh',
    'Uttarakhand',
    'West Bengal',
    'Andaman and Nicobar Islands',
    'Chandigarh',
    'Dadra and Nagar Haveli and Daman and Diu',
    'Delhi',
    'Jammu and Kashmir',
    'Ladakh',
    'Lakshadweep',
    'Puducherry',
  ];

  @override
  void initState() {
    super.initState();

    debugPrint('🔔 RFID: ========== RFID ORDER FORM INIT START ==========');
    debugPrint('🔔 RFID: Widget mounted: $mounted');
    debugPrint('🔔 RFID: Profile data provided: ${widget.profileData != null}');

    try {
      // Pre-populate form fields with profile data
      _populateFormFields();
      debugPrint('✅ RFID: Form fields populated successfully');
    } catch (e) {
      debugPrint('❌ RFID: Error populating form fields: $e');
    }

    // Verify API configuration in debug mode
    if (kDebugMode) {
      try {
        _apiService.verifyRfidApiConfiguration();
        debugPrint('✅ RFID: API configuration verified');
      } catch (e) {
        debugPrint('❌ RFID: Error verifying API configuration: $e');
      }
    }

    debugPrint('🔔 RFID: ========== RFID ORDER FORM INIT END ==========');
  }

  void _populateFormFields() {
    if (widget.profileData != null) {
      final profile = widget.profileData!;

      // Pre-populate form fields with API data
      _nameController.text = profile.name;
      _emailController.text = profile.email;
      _phoneController.text = profile.mobileNumber;
      _addressController.text = profile.address;
      _stateController.text = profile.state;
      _pincodeController.text = profile.pincode;

      // Pre-populate GST and business name if available
      if (profile.gstNo != null && profile.gstNo!.isNotEmpty) {
        _gstController.text = profile.gstNo!;
      }
      if (profile.businessName != null && profile.businessName!.isNotEmpty) {
        _businessNameController.text = profile.businessName!;
      }

      // Set default amount if needed
      _amountController.text = '500'; // Base RFID card cost (may vary based on location and taxes)
    }
  }

  /// Validate email format
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter an email address';
    }
    final emailRegex = RegExp(r'^\w+([\.-]?\w+)*@\w+([\.-]?\w+)+$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  /// Validate name
  String? _validateName(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your full name';
    }
    if (value.length < 3) {
      return 'Name must be at least 3 characters long';
    }
    return null;
  }

  /// Validate address
  String? _validateAddress(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter your address';
    }
    if (value.length < 10) {
      return 'Address must be at least 10 characters long';
    }
    return null;
  }

  /// Validate state
  String? _validateState(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please select a state';
    }
    return null;
  }

  /// Validate pincode
  String? _validatePincode(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter pincode';
    }
    final pincodeRegex = RegExp(r'^\d{6}$');
    if (!pincodeRegex.hasMatch(value)) {
      return 'Pincode must be a 6-digit number';
    }
    return null;
  }

  /// Validate amount
  String? _validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter amount';
    }
    final amount = double.tryParse(value);
    if (amount == null || amount < 500) {
      return 'Please enter a valid amount! Minimum amount is ₹500 (may vary based on location)';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🔔 RFID: ========== BUILD METHOD START ==========');
    debugPrint('🔔 RFID: Widget mounted: $mounted');
    debugPrint('🔔 RFID: Loading state: $_isLoading');
    debugPrint('🔔 RFID: Payment in progress: $_isPaymentInProgress');

    // Safety check for widget state
    if (!mounted) {
      debugPrint('❌ RFID: Widget not mounted, returning empty container');
      return Container();
    }

    try {
      return Scaffold(
        backgroundColor: const Color(0xFF0E0E0E), // Dark background

        appBar: AppBar(
          backgroundColor: const Color(0xFF0E0E0E),
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back,
                color: Color(0xFF67C44C)), // Green arrow
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text(
            'RFID Order Form',
            style: TextStyle(color: Colors.white),
          ),
        ),

        // Use a Column so we can pin the "Submit Order" button at the bottom.
        body: SafeArea(
          child: Column(
            children: [
              // 1) Scrollable Form content
              Expanded(
                child: SingleChildScrollView(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Contact Info Section
                        const Text(
                          'Contact Information',
                          style: TextStyle(
                            color: Color(0xFF67C44C),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Full Name
                        _buildTextField(
                          label: 'Full Name *',
                          controller: _nameController,
                          keyboardType: TextInputType.text,
                          validator: _validateName,
                        ),
                        const SizedBox(height: 8),
                        // Email
                        _buildTextField(
                          label: 'Email Address *',
                          controller: _emailController,
                          keyboardType: TextInputType.emailAddress,
                          validator: _validateEmail,
                        ),
                        const SizedBox(height: 8),
                        // Phone
                        _buildTextField(
                          label: 'Phone Number *',
                          controller: _phoneController,
                          keyboardType: TextInputType.phone,
                          validator: (value) => (value == null || value.isEmpty)
                              ? 'Please enter your phone number'
                              : null,
                        ),
                        const SizedBox(height: 16),

                        // Address Info Section
                        const Text(
                          'Address Information',
                          style: TextStyle(
                            color: Color(0xFF67C44C),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Street Address
                        _buildTextField(
                          label: 'Street Address *',
                          controller: _addressController,
                          keyboardType: TextInputType.streetAddress,
                          validator: _validateAddress,
                        ),
                        const SizedBox(height: 8),

                        // State & Pincode in one row with proper constraints
                        IntrinsicHeight(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // State
                              Expanded(
                                flex: 1,
                                child: _buildStateDropdown(),
                              ),
                              const SizedBox(width: 12),
                              // Pincode
                              Expanded(
                                flex: 1,
                                child: _buildTextField(
                                  label: 'Pincode *',
                                  controller: _pincodeController,
                                  keyboardType: TextInputType.number,
                                  validator: _validatePincode,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Payment Details Section
                        const Text(
                          'Payment Details',
                          style: TextStyle(
                            color: Color(0xFF67C44C),
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // Amount
                        _buildTextField(
                          label: 'Amount *',
                          controller: _amountController,
                          keyboardType: TextInputType.number,
                          validator: _validateAmount,
                        ),
                        const SizedBox(height: 16),

                        // Business Details Section (Optional)
                        if (widget.profileData?.gstNo != null ||
                            widget.profileData?.businessName != null) ...[
                          const Text(
                            'Business Details (Optional)',
                            style: TextStyle(
                              color: Color(0xFF67C44C),
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Business Name
                          if (widget.profileData?.businessName != null)
                            _buildTextField(
                              label: 'Business Name',
                              controller: _businessNameController,
                              keyboardType: TextInputType.text,
                            ),
                          const SizedBox(height: 8),

                          // GST Number
                          if (widget.profileData?.gstNo != null)
                            _buildTextField(
                              label: 'GST Number',
                              controller: _gstController,
                              keyboardType: TextInputType.text,
                            ),
                          const SizedBox(height: 16),
                        ],
                      ],
                    ),
                  ),
                ),
              ),

              // 2) Pinned Submit Button at the bottom
              Container(
                color: const Color(0xFF0E0E0E),
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: SizedBox(
                  width: double.infinity,
                  height: 44, // Slightly smaller height
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF67C44C), // Green accent
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onPressed: (_isLoading || _isPaymentInProgress)
                        ? null
                        : _handleSubmit,
                    child: (_isLoading || _isPaymentInProgress)
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  color: Colors.black,
                                  strokeWidth: 2,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                _isPaymentInProgress
                                    ? 'Processing Payment...'
                                    : 'Submitting...',
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          )
                        : const Text(
                            'Submit Order & Pay',
                            style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('❌ RFID: Error in build method: $e');
      debugPrint('❌ RFID: Stack trace: $stackTrace');

      // Return error screen
      return Scaffold(
        backgroundColor: const Color(0xFF0E0E0E),
        appBar: AppBar(
          backgroundColor: const Color(0xFF0E0E0E),
          title: const Text(
            'RFID Order Form',
            style: TextStyle(color: Colors.white),
          ),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 64,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Error Loading RFID Order Form',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Error: $e',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      // Trigger rebuild
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF67C44C),
                  ),
                  child: const Text(
                    'Retry',
                    style: TextStyle(color: Colors.black),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    required TextInputType keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 13, // Slightly smaller label text
          ),
        ),
        const SizedBox(height: 4),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          style: const TextStyle(color: Colors.white, fontSize: 14),
          validator: validator,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
            filled: true,
            fillColor: const Color(0xFF1E1E1E),
            hintText: _hintForLabel(label),
            hintStyle: const TextStyle(color: Colors.white38, fontSize: 13),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.transparent),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  // Provide placeholder text based on label if desired.
  String _hintForLabel(String label) {
    if (label.toLowerCase().contains('full name')) {
      return 'Enter your full name';
    } else if (label.toLowerCase().contains('email')) {
      return 'Enter your email address';
    } else if (label.toLowerCase().contains('phone')) {
      return 'Enter your phone number';
    } else if (label.toLowerCase().contains('street address')) {
      return 'Enter your street address';
    } else if (label.toLowerCase().contains('state')) {
      return 'Enter your state';
    } else if (label.toLowerCase().contains('pincode')) {
      return 'Enter 6-digit pincode';
    } else if (label.toLowerCase().contains('amount')) {
      return 'Enter amount';
    } else if (label.toLowerCase().contains('business name')) {
      return 'Enter your business name';
    } else if (label.toLowerCase().contains('gst')) {
      return 'Enter your GST number';
    }
    return '';
  }

  /// Handle form submission with PhonePe payment integration
  /// Following the React code flow: validate → order creation → PhonePe payment
  Future<void> _handleSubmit() async {
    debugPrint('🔔 RFID: ========== RFID ORDER SUBMISSION START ==========');

    // Validate form fields (matching React validation logic)
    if (!(_formKey.currentState?.validate() ?? false)) {
      debugPrint('❌ RFID: Form validation failed');
      return;
    }

    // Additional amount validation (matching React code)
    final amount = double.tryParse(_amountController.text.trim());
    if (amount == null || amount < 500) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'Please enter a valid amount! Minimum amount is ₹500 (may vary based on location)'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    setState(() {
      _isLoading = true;
      _isPaymentInProgress = true;
      _pendingPaymentAmount = amount;
    });

    try {
      debugPrint('🔔 RFID: Creating order data...');

      // Create order data (matching React formData structure)
      final orderData = RfidOrderData(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        mobileNumber: _phoneController.text.trim(),
        address: _addressController.text.trim(),
        state: _stateController.text.trim(),
        pincode: _pincodeController.text.trim(),
        amount: _amountController.text.trim(),
      );

      debugPrint('🔔 RFID: Calling order initialization API...');

      // Initialize order with API (matching React: axios.post('/orders/init-v2', formData))
      final response =
          await _apiService.initializeRfidOrder(orderData.toJson());

      debugPrint(
          '🔔 RFID: Order API response received: ${response['success']}');

      if (response['success'] == true) {
        debugPrint(
            '✅ RFID: Order created successfully, initiating server-determined payment...');

        // Extract order details for payment (matching React: let order = res.data)
        final orderResponse = response;

        // Use server-side payment method determination (following wallet screen pattern)
        await _initiateServerDeterminedPayment(orderResponse, amount);
      } else {
        // Handle order creation failure
        setState(() {
          _isLoading = false;
          _isPaymentInProgress = false;
          _pendingPaymentAmount = null;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Failed to place order'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ RFID: Exception during order creation: $e');

      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
      });

      String errorMessage = 'Failed to place order';
      if (e is ApiException) {
        errorMessage = e.message;
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Initiate server-determined payment gateway based on payment_option from order response
  /// Following the exact same pattern as wallet screen server-side payment method selection
  Future<void> _initiateServerDeterminedPayment(
      Map<String, dynamic> orderResponse, double amount) async {
    debugPrint(
        '💳 RFID SERVER_DETERMINED: Starting server-determined payment for amount: ₹$amount');

    try {
      // Extract payment_option from order response (same as wallet response structure)
      String serverPaymentOption = 'payu'; // Default fallback

      if (orderResponse.containsKey('payment_option') &&
          orderResponse['payment_option'] != null &&
          orderResponse['payment_option'].toString().isNotEmpty) {
        serverPaymentOption =
            orderResponse['payment_option'].toString().toLowerCase().trim();
        debugPrint(
            '💳 RFID SERVER_DETERMINED: Server payment_option: "$serverPaymentOption"');
      } else {
        debugPrint(
            '💳 RFID SERVER_DETERMINED: No payment_option in order response, using default: $serverPaymentOption');
      }

      // Use if-else to determine which payment gateway to initiate based on server's payment_option
      if (serverPaymentOption == 'phonepe') {
        debugPrint(
            '💳 RFID SERVER_DETERMINED: Initiating PhonePe payment as per server preference');
        await _initPhonePeSDK(orderResponse, amount);
      } else if (serverPaymentOption == 'payu') {
        debugPrint(
            '💳 RFID SERVER_DETERMINED: Initiating PayU payment as per server preference');
        await _initPayUSDK(orderResponse, amount);
      } else if (serverPaymentOption == 'cashfree') {
        debugPrint(
            '💳 RFID SERVER_DETERMINED: Initiating Cashfree payment as per server preference');
        await _initCashfreeSDK(orderResponse, amount);
      } else {
        // Fallback to PayU for any unrecognized payment option
        debugPrint(
            '💳 RFID SERVER_DETERMINED: Unrecognized payment_option "$serverPaymentOption", falling back to PayU');
        await _initPayUSDK(orderResponse, amount);
      }
    } catch (e) {
      debugPrint(
          '❌ RFID SERVER_DETERMINED: Error in server-determined payment: $e');

      // Fallback to PayU on any error
      debugPrint(
          '💳 RFID SERVER_DETERMINED: Falling back to PayU due to error');
      await _initPayUSDK(orderResponse, amount);
    }
  }

  /// Initialize PhonePe SDK and start payment transaction
  /// Following the wallet screen pattern with separate PhonePe initiation API call
  Future<void> _initPhonePeSDK(
      Map<String, dynamic> order, double amount) async {
    debugPrint(
        '🔔 RFID: ========== PHONEPE SDK INITIALIZATION START ==========');
    debugPrint('🔔 RFID: Payment amount: ₹$amount');

    try {
      // Show payment initiation loading
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Initializing PhonePe payment...'),
              ],
            ),
            duration: Duration(seconds: 30),
          ),
        );
      }

      debugPrint('🔔 RFID: Calling PhonePe initiation API...');

      // Call PhonePe initiation API (following wallet screen pattern)
      final requestData = {
        'amount': amount,
        'source': 'app',
        'order_id': order['txnId'] ??
            order['transaction_id'] ??
            'RFID_${DateTime.now().millisecondsSinceEpoch}',
      };

      debugPrint('🔔 RFID: PhonePe initiation request: $requestData');

      final payload = await _apiService.post(
        '/user/payment/initiate-phonepev2', // Same endpoint as wallet screen
        data: requestData,
      );

      debugPrint('🔔 RFID: PhonePe initiation response received');
      debugPrint('🔔 RFID: Response keys: ${payload.keys.toList()}');

      // Validate response structure
      if (payload['success'] != true) {
        final message = payload['message'] ?? 'PhonePe initiation failed';
        debugPrint('❌ RFID: Server returned non-success status');
        throw Exception('PhonePe initiation failed: $message');
      }

      // Extract values from payload response with null safety
      final environment = payload['environment']?.toString();
      final merchantId = payload['merchantId']?.toString() ??
          payload['merchant_id']?.toString();
      final txnId = payload['txnId']?.toString() ??
          payload['transaction_id']?.toString() ??
          payload['txn_id']?.toString();

      // Validate required parameters
      if (environment == null || environment.isEmpty) {
        throw Exception(
            'PhonePe environment is missing from server response. Available keys: ${payload.keys.toList()}');
      }
      if (merchantId == null || merchantId.isEmpty) {
        throw Exception(
            'PhonePe merchantId is missing from server response. Available keys: ${payload.keys.toList()}');
      }
      if (txnId == null || txnId.isEmpty) {
        throw Exception(
            'PhonePe txnId is missing from server response. Available keys: ${payload.keys.toList()}');
      }

      // Store transaction ID for response handling
      _currentTransactionId = txnId;

      debugPrint('🔔 RFID: Environment: $environment');
      debugPrint('🔔 RFID: MerchantId: $merchantId');
      debugPrint('🔔 RFID: TxnId: $txnId');

      // Initialize PhonePe SDK (matching React: phonepeSDK.init())
      final flowId = 'rfid_${txnId}_${DateTime.now().millisecondsSinceEpoch}';
      final inited = await PhonePeService.init(
        environment: environment,
        merchantId: merchantId,
        flowId: flowId,
        enableLogging: true,
      );

      if (!inited) {
        throw Exception('PhonePe SDK initialization failed');
      }

      debugPrint('✅ RFID: PhonePe SDK initialized successfully');

      // Prepare payment request (using payload from server response)
      final paymentRequest = {
        "orderId": payload['payload']['orderId'],
        "merchantId": payload['payload']['merchantId'],
        "token": payload['payload']['token'],
        "paymentMode": {"type": "PAY_PAGE"}
      };

      final requestBody = jsonEncode(paymentRequest);
      const appSchema = 'com.eeil.ecoplug';

      debugPrint('🔔 RFID: Starting PhonePe transaction...');

      // Update loading message
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Opening PhonePe...'),
              ],
            ),
            duration: Duration(minutes: 5),
          ),
        );
      }

      // Start PhonePe transaction (matching React: phonepeSDK.startTransaction())
      final result = await PhonePeService.startPGTransaction(
        request: requestBody,
        appSchema: appSchema,
        timeout: const Duration(minutes: 5),
      );

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      debugPrint(
          '🔔 RFID: PhonePe transaction completed with result: ${result.type}');

      // Handle payment result (matching React: PhonepeResponse(res, order.txnId))
      await _handlePhonepeResponse(result, txnId);
    } catch (e) {
      debugPrint('❌ RFID: PhonePe payment error: $e');

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      // Note: Removed error snackbar as payment gateway navigation might still work
      // Error details are logged for debugging purposes
    }
  }

  /// Handle PhonePe payment response
  /// Matching React code: PhonepeResponse(res, txnId, checksum)
  Future<void> _handlePhonepeResponse(
      PaymentResult result, String txnId) async {
    debugPrint(
        '🔔 RFID: ========== PHONEPE RESPONSE HANDLING START ==========');
    debugPrint('🔔 RFID: Result type: ${result.type}');
    debugPrint('🔔 RFID: Transaction ID: $txnId');

    try {
      final payload = {
        'code': (result.data ?? {})['statusCode']?.toString() ??
            (result.data ?? {})['status']?.toString() ??
            result.type.toString().split('.').last.toUpperCase(),
        'transactionId': txnId,
        'response': result.data ?? {},
        'call_back_response': result.data ?? {},
      };

      debugPrint('🔔 RFID: Sending payment response to server...');

      // Send response to server (matching React: axios.post('/payment/response-phonepev2'))
      final response = await _apiService.handlePhonePeResponse(payload);

      debugPrint('🔔 RFID: Server response received: ${response['success']}');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        if (response['success'] == true) {
          // Payment successful (matching React: snackBarSuccess(data.message))
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(response['message'] ??
                        'RFID order payment successful!'),
                  ),
                ],
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 4),
            ),
          );

          // Show success dialog and navigate back (matching React: props.navigation.goBack())
          _showPaymentSuccessDialog(response);
        } else {
          // Payment failed (matching React: snackBarError(data.message))
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response['message'] ?? 'Payment failed'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ RFID: Error handling payment response: $e');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment processing failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Initialize PayU SDK and start payment transaction
  /// Following the wallet screen pattern with separate PayU initiation API call
  Future<void> _initPayUSDK(Map<String, dynamic> order, double amount) async {
    debugPrint('🔔 RFID: ========== PAYU SDK INITIALIZATION START ==========');
    debugPrint('🔔 RFID: Payment amount: ₹$amount');
    debugPrint('🔔 RFID: Widget mounted: $mounted');

    if (!mounted) {
      debugPrint('❌ RFID: Widget not mounted, aborting PayU payment');
      debugPrint(
          '🔔 RFID: ========== PAYU SDK INITIALIZATION END (NOT MOUNTED) ==========');
      return;
    }

    try {
      debugPrint('🔔 RFID: Calling PayU initiation API...');

      // Call PayU initiation API (following wallet screen pattern)
      final requestData = {
        'amount': amount,
        'source': 'app',
        'order_id': order['txnId'] ??
            order['transaction_id'] ??
            'RFID_${DateTime.now().millisecondsSinceEpoch}',
      };

      debugPrint('🔔 RFID: PayU initiation request: $requestData');

      final payload = await _apiService.post(
        '/user/payment/initiate-payu', // Same endpoint as wallet screen
        data: requestData,
      );

      debugPrint('🔔 RFID: PayU initiation response received');
      debugPrint('🔔 RFID: Response keys: ${payload.keys.toList()}');

      // Validate response structure
      if (payload['success'] != true) {
        final message = payload['message'] ?? 'PayU initiation failed';
        debugPrint('❌ RFID: Server returned non-success status');
        throw Exception('PayU initiation failed: $message');
      }

      // Extract PayU parameters from server response (following wallet pattern)
      String? merchantKey;
      String? environment;
      String? txnId;

      // First try to extract from nested payUPaymentParams object (actual server format)
      if (payload.containsKey('payUPaymentParams') &&
          payload['payUPaymentParams'] is Map) {
        final payUParams = payload['payUPaymentParams'] as Map<String, dynamic>;
        merchantKey = payUParams['key']?.toString();
        environment = payUParams['environment']?.toString();
        txnId = payUParams['txnid']?.toString() ??
            payUParams['transactionId']?.toString();
        debugPrint('🔔 RFID: Extracted from payUPaymentParams nested object');
      }

      // Fallback: try root level with different field name variations
      if (environment == null) {
        environment =
            payload['environment']?.toString() ?? payload['env']?.toString();
        debugPrint('🔔 RFID: Extracted environment from root level');
      }

      if (merchantKey == null) {
        merchantKey = payload['merchantKey']?.toString() ??
            payload['merchant_key']?.toString() ??
            payload['key']?.toString();
        debugPrint('🔔 RFID: Extracted merchantKey from root level');
      }

      if (txnId == null) {
        txnId = payload['txnId']?.toString() ??
            payload['transaction_id']?.toString() ??
            payload['txn_id']?.toString();
        debugPrint('🔔 RFID: Extracted txnId from root level');
      }

      debugPrint('🔔 RFID: Extracted parameters:');
      debugPrint(
          '🔔 RFID: merchantKey: ${merchantKey != null ? (merchantKey.length > 8 ? "${merchantKey.substring(0, 8)}..." : merchantKey) : "NULL"}');
      debugPrint('🔔 RFID: environment: $environment');
      debugPrint('🔔 RFID: txnId: $txnId');

      // Validate required parameters
      if (merchantKey == null || merchantKey.isEmpty) {
        debugPrint('❌ RFID: merchantKey validation failed');
        debugPrint(
            '❌ RFID: Available keys in response: ${payload.keys.toList()}');
        throw Exception(
            'PayU merchantKey is missing from server response. Available keys: ${payload.keys.toList()}');
      }
      if (environment == null || environment.isEmpty) {
        debugPrint('❌ RFID: environment validation failed');
        throw Exception(
            'PayU environment is missing from server response. Available keys: ${payload.keys.toList()}');
      }
      if (txnId == null || txnId.isEmpty) {
        debugPrint('❌ RFID: txnId validation failed');
        throw Exception(
            'PayU txnId is missing from server response. Available keys: ${payload.keys.toList()}');
      }

      // Store transaction ID for response handling
      _currentTransactionId = txnId;

      debugPrint('🔔 RFID: Environment: $environment');
      debugPrint(
          '🔔 RFID: MerchantKey: ${merchantKey.length > 8 ? "${merchantKey.substring(0, 8)}..." : merchantKey}');
      debugPrint('🔔 RFID: TxnId: $txnId');

      debugPrint('🔔 RFID: ========== PAYU SDK INITIALIZATION ==========');
      // Initialize PayU SDK
      final inited = await PayUService.init(
        merchantKey: merchantKey,
        environment: environment,
        enableLogging: true,
      );

      if (!inited) {
        throw Exception('PayU SDK initialization failed');
      }

      debugPrint('✅ RFID: PayU SDK initialized successfully');

      // Use the complete payUPaymentParams from server response (same as wallet)
      Map<String, dynamic> paymentParams;

      if (payload.containsKey('payUPaymentParams') &&
          payload['payUPaymentParams'] is Map) {
        // Use server-provided payment parameters
        paymentParams = Map<String, dynamic>.from(
            payload['payUPaymentParams'] as Map<String, dynamic>);
        debugPrint('🔔 RFID: Using server-provided payUPaymentParams');
        debugPrint(
            '🔔 RFID: Server params keys: ${paymentParams.keys.toList()}');

        // Ensure required fields are properly formatted with correct PayU parameter names
        paymentParams['key'] = merchantKey;
        paymentParams['txnid'] = txnId;
        paymentParams['amount'] = amount.toString();
        paymentParams['environment'] = environment;

        // Fix parameter name mapping for PayU SDK requirements
        if (paymentParams.containsKey('productInfo')) {
          paymentParams['productinfo'] = paymentParams['productInfo'];
          paymentParams.remove('productInfo');
        }
        if (paymentParams.containsKey('firstName')) {
          paymentParams['firstname'] = paymentParams['firstName'];
          paymentParams.remove('firstName');
        }

        // Override with RFID-specific user data
        paymentParams['firstname'] = _nameController.text.trim();
        paymentParams['email'] = _emailController.text.trim();
        paymentParams['phone'] = _phoneController.text.trim();
        paymentParams['productinfo'] = 'EcoPlug RFID Card';

        // Add mobile app specific URLs if not present
        if (!paymentParams.containsKey('surl')) {
          paymentParams['surl'] = 'com.eeil.ecoplug://payu/success';
        }
        if (!paymentParams.containsKey('furl')) {
          paymentParams['furl'] = 'com.eeil.ecoplug://payu/failure';
        }

        debugPrint(
            '🔔 RFID: Using server-provided payment parameters with RFID-specific data');
      } else {
        // Fallback: Build PayU payment parameters manually
        debugPrint(
            '🔔 RFID: Server payUPaymentParams not found, building manually');
        paymentParams = {
          // Required PayU parameters
          'key': merchantKey,
          'txnid': txnId,
          'amount': amount.toString(),
          'productinfo': 'EcoPlug RFID Card',
          'firstname': _nameController.text.trim(),
          'email': _emailController.text.trim(),
          'phone': _phoneController.text.trim(),

          // Success and failure URLs
          'surl': 'com.eeil.ecoplug://payu/success',
          'furl': 'com.eeil.ecoplug://payu/failure',

          // Environment and additional parameters
          'environment': environment,
          'userCredential':
              '$merchantKey:rfid_${DateTime.now().millisecondsSinceEpoch}',

          // Optional parameters for better UX
          'udf1': 'rfid_card',
          'udf2': 'ecoplug_app',
          'udf3': '',
          'udf4': '',
          'udf5': '',
        };
      }

      debugPrint('🔔 RFID: Starting PayU transaction...');

      // Update loading message
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Opening PayU...'),
              ],
            ),
            duration: Duration(minutes: 5),
          ),
        );
      }

      // Start PayU transaction
      final result = await PayUService.startPayment(
        paymentParams: paymentParams,
        timeout: const Duration(minutes: 5),
      );

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      debugPrint(
          '🔔 RFID: PayU transaction completed with result: ${result.type}');

      // Handle payment result
      await _handlePayUResponse(result, txnId);
    } catch (e) {
      debugPrint('❌ RFID: PayU payment error: $e');

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      // Note: Removed error snackbar as payment gateway navigation might still work
      // Error details are logged for debugging purposes
    }
  }

  /// Enhanced PayU payment response handler for RFID orders
  Future<void> _handlePayUResponse(
      PayUPaymentResult result, String txnId) async {
    debugPrint(
        '🔔 RFID: ========== ENHANCED PAYU RESPONSE HANDLING START ==========');
    debugPrint('🔔 RFID: Result type: ${result.type}');
    debugPrint('🔔 RFID: Transaction ID: $txnId');
    debugPrint('🔔 RFID: Raw result data: ${result.data}');

    try {
      // Normalize PayU status for backend compatibility
      final normalizedStatus =
          _normalizePayUStatus(result.data?['status']?.toString());
      debugPrint('🔔 RFID: Status normalized to: $normalizedStatus');

      // Create standardized payload matching wallet implementation
      final payload =
          _createStandardizedPayUPayload(result, txnId, normalizedStatus);

      debugPrint('🔔 RFID: Sending standardized PayU payload to server...');
      debugPrint('🔔 RFID: Payload: ${jsonEncode(payload)}');

      // Send response to server with enhanced error handling
      final response = await _apiService.handlePayUResponse(payload);

      debugPrint('🔔 RFID: Server response received');
      debugPrint('🔔 RFID: Response type: ${response.runtimeType}');
      debugPrint('🔔 RFID: Response content: $response');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        if (response['success'] == true) {
          // Payment successful - use user-friendly message
          debugPrint('✅ RFID: Server response: ${response['message']}');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('RFID card payment completed successfully!'),
              backgroundColor: Colors.green,
            ),
          );

          // Show success dialog and navigate back
          _showPaymentSuccessDialog(response);
        } else {
          // Payment failed - use user-friendly message, log raw message
          debugPrint('❌ RFID: Server error message: ${response['message']}');
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('RFID card payment failed. Please try again.'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ RFID: Error handling PayU payment response: $e');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('PayU payment processing failed: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Initialize Cashfree SDK and start payment transaction
  /// Following the wallet screen pattern with separate Cashfree initiation API call
  Future<void> _initCashfreeSDK(
      Map<String, dynamic> order, double amount) async {
    debugPrint(
        '🔔 RFID: ========== CASHFREE SDK INITIALIZATION START ==========');
    debugPrint('🔔 RFID: Payment amount: ₹$amount');
    debugPrint('🔔 RFID: Widget mounted: $mounted');

    if (!mounted) {
      debugPrint('❌ RFID: Widget not mounted, aborting Cashfree payment');
      debugPrint(
          '🔔 RFID: ========== CASHFREE SDK INITIALIZATION END (NOT MOUNTED) ==========');
      return;
    }

    try {
      debugPrint('🔔 RFID: Calling Cashfree initiation API...');

      // Call Cashfree initiation API (following wallet screen pattern)
      final requestData = {
        'amount': amount,
        'source': 'app',
        'order_id': order['txnId'] ??
            order['transaction_id'] ??
            'RFID_${DateTime.now().millisecondsSinceEpoch}',
      };

      debugPrint('🔔 RFID: Cashfree initiation request: $requestData');

      final payload = await _apiService.post(
        '/user/payment/initiate-cashfree', // Same endpoint as wallet screen
        data: requestData,
      );

      debugPrint('🔔 RFID: Cashfree initiation response received');
      debugPrint('🔔 RFID: Response keys: ${payload.keys.toList()}');

      // Validate response structure
      if (payload['success'] != true) {
        final message = payload['message'] ?? 'Cashfree initiation failed';
        debugPrint('❌ RFID: Server returned non-success status');
        throw Exception('Cashfree initiation failed: $message');
      }

      // Extract nested order data (Cashfree uses nested 'order' object)
      final orderData = payload['order'];
      if (orderData == null) {
        throw Exception('Order data not found in server response');
      }

      debugPrint('🔔 RFID: Order data keys: ${orderData.keys.toList()}');

      // Extract required parameters from nested structure (following wallet pattern)
      final orderId = orderData['order_id']?.toString() ??
          orderData['cf_order_id']?.toString();
      final paymentSessionId = orderData['payment_session_id']?.toString() ??
          orderData['session_id']?.toString();
      final environment = payload['environment']?.toString() ??
          orderData['environment']?.toString() ??
          'sandbox';
      final txnId = payload['txnId']?.toString() ??
          orderData['order_id']?.toString() ??
          orderData['cf_order_id']?.toString() ??
          'RFID_${DateTime.now().millisecondsSinceEpoch}';

      debugPrint('🔔 RFID: Extracted Cashfree parameters:');
      debugPrint('🔔 RFID: orderId: $orderId');
      debugPrint('🔔 RFID: paymentSessionId: $paymentSessionId');
      debugPrint('🔔 RFID: environment: $environment');
      debugPrint('🔔 RFID: txnId: $txnId');

      // Validate required parameters
      if (orderId == null || orderId.isEmpty) {
        throw Exception('Order ID not found in server response');
      }
      if (paymentSessionId == null || paymentSessionId.isEmpty) {
        throw Exception('Payment session ID not found in server response');
      }
      if (txnId.isEmpty) {
        throw Exception('Transaction ID could not be generated');
      }

      // Store transaction ID for response handling
      _currentTransactionId = txnId;

      debugPrint('🔔 RFID: Starting Cashfree payment...');

      // Update loading message
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 16),
                Text('Opening Cashfree...'),
              ],
            ),
            duration: Duration(minutes: 5),
          ),
        );
      }

      // Start Cashfree transaction
      final result = await CashfreeService.startWebCheckoutTransaction(
        orderId: orderId,
        paymentSessionId: paymentSessionId,
        environment: environment.toLowerCase() == 'production'
            ? CFEnvironment.PRODUCTION
            : CFEnvironment.SANDBOX,
        timeout: const Duration(minutes: 5),
      );

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      debugPrint(
          '🔔 RFID: Cashfree transaction completed with result: ${result.type}');

      // Handle payment result
      await _handleCashfreeResponse(result, txnId);
    } catch (e) {
      debugPrint('❌ RFID: Cashfree payment error: $e');

      // Clear loading snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).clearSnackBars();
      }

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      // Note: Removed error snackbar as payment gateway navigation might still work
      // Error details are logged for debugging purposes
    }
  }

  /// Handle Cashfree payment response - ONLY backend response determines UI
  /// Backend team confirmed: Always send orderId to handleResponse endpoint for all cases
  Future<void> _handleCashfreeResponse(
      CashfreePaymentResult result, String txnId) async {
    debugPrint(
        '🔔 RFID: ========== CASHFREE RESPONSE HANDLING START ==========');
    debugPrint('🔔 RFID: SDK Callback type: ${result.data?['callbackType']}');
    debugPrint('🔔 RFID: Transaction ID: $txnId');
    debugPrint('🔔 RFID: IGNORING SDK callback type - backend will determine success/failure');

    try {
      // Extract orderId from result data (set by the SDK callbacks)
      String orderId = result.data?['orderId']?.toString() ?? txnId;
      debugPrint('🔔 RFID: Using orderId: $orderId');

      // CRITICAL: Always send orderId to backend - backend response is ONLY source of truth
      final payload = {
        'transactionId': orderId, // This will be extracted as orderId by ApiService
      };

      debugPrint('🔔 RFID: ========== BACKEND API CALL START ==========');
      debugPrint('🔔 RFID: Sending orderId to backend...');
      debugPrint('🔔 RFID: Backend response will determine ALL UI feedback');
      debugPrint('🔔 RFID: Payload: $payload');

      // Send response to server using centralized ApiService method
      final response = await _apiService.handleCashfreeResponse(payload);

      debugPrint('🔔 RFID: ========== BACKEND RESPONSE RECEIVED ==========');
      debugPrint('🔔 RFID: Backend response success: ${response['success']}');
      debugPrint('🔔 RFID: Backend response message: ${response['message']}');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        // CRITICAL: ONLY backend response determines UI - ignore SDK callback completely
        if (response['success'] == true) {
          debugPrint('✅ RFID: ========== BACKEND CONFIRMED SUCCESS ==========');
          debugPrint('✅ RFID: Backend says payment was SUCCESSFUL');
          debugPrint('✅ RFID: Payment successful (silent mode) - no dialog shown');
          // SILENT PAYMENT: Success confirmed by backend - no dialog shown
        } else {
          debugPrint('❌ RFID: ========== BACKEND CONFIRMED FAILURE ==========');
          debugPrint('❌ RFID: Backend says payment FAILED');
          final errorMessage = response['message'] ?? 'Payment failed';
          debugPrint('❌ RFID: Backend error message: $errorMessage');
          debugPrint('❌ RFID: Showing ERROR message based on backend response');

          // SILENT PAYMENT: Error logged but no SnackBar shown to user
          debugPrint('❌ RFID: Payment failed (silent mode): $errorMessage');
        }
      }
    } on ApiException catch (e) {
      debugPrint('❌ RFID: API Exception: ${e.message} (Code: ${e.code})');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        // SILENT PAYMENT: API error logged but no SnackBar shown
        debugPrint('❌ RFID: API error (silent mode): ${e.message}');
      }
    } catch (e) {
      debugPrint('❌ RFID: Unexpected error handling Cashfree payment response: $e');

      // Reset payment state
      setState(() {
        _isLoading = false;
        _isPaymentInProgress = false;
        _pendingPaymentAmount = null;
        _currentTransactionId = null;
      });

      if (mounted) {
        // SILENT PAYMENT: Unexpected error logged but no SnackBar shown
        debugPrint('❌ RFID: Unexpected error (silent mode): ${e.toString()}');
      }
    }

    debugPrint(
        '🔔 RFID: ========== CASHFREE RESPONSE HANDLING END ==========');
  }

  /// Show payment success dialog
  void _showPaymentSuccessDialog(Map<String, dynamic> response) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A1A1A),
          title: const Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 24),
              SizedBox(width: 8),
              Text(
                'Payment Successful!',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Your RFID card order has been placed and payment completed successfully.',
                style: TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              if (_currentTransactionId != null) ...[
                Text(
                  'Transaction ID: $_currentTransactionId',
                  style: const TextStyle(
                      color: Colors.white, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
              ],
              Text(
                'Amount Paid: ₹${_amountController.text}',
                style: const TextStyle(color: Colors.white),
              ),
              const SizedBox(height: 8),
              const Text(
                'You will receive your RFID card within 7-10 business days.',
                style: TextStyle(color: Colors.white70),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                Navigator.of(context)
                    .pop(); // Go back to previous screen (matching React: props.navigation.goBack())
              },
              child: const Text(
                'OK',
                style: TextStyle(color: Color(0xFF67C44C)),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build state dropdown widget with proper dark mode support and fixed layout
  Widget _buildStateDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          'State *',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 13,
          ),
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<String>(
          value: _stateController.text.isEmpty ? null : _stateController.text,
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            filled: true,
            fillColor: const Color(0xFF1E1E1E),
            hintText: 'Select State',
            hintStyle: const TextStyle(color: Colors.white38, fontSize: 13),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.transparent),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            errorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide: const BorderSide(color: Colors.red),
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          dropdownColor: const Color(0xFF1E1E1E),
          style: const TextStyle(color: Colors.white, fontSize: 14),
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white70),
          validator: _validateState,
          isExpanded: true,
          items: _indianStates.isNotEmpty
              ? _indianStates.map((String state) {
                  return DropdownMenuItem<String>(
                    value: state,
                    child: Text(
                      state,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                }).toList()
              : [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text(
                      'Loading states...',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ),
                ],
          onChanged: (String? newValue) {
            if (newValue != null) {
              setState(() {
                _stateController.text = newValue;
              });
            }
          },
        ),
      ],
    );
  }

  /// Normalize PayU status to backend-expected format (matching wallet implementation)
  String _normalizePayUStatus(String? status) {
    if (status == null || status.isEmpty) {
      debugPrint('⚠️ RFID PAYU: Empty status, defaulting to "unknown"');
      return 'unknown';
    }

    final statusLower = status.toLowerCase().trim();

    // Success statuses
    if (['success', 'completed', 'successful', 'complete']
        .contains(statusLower)) {
      return 'success';
    }

    // Failure statuses
    if (['failure', 'failed', 'error', 'rejected'].contains(statusLower)) {
      return 'failure';
    }

    // Cancelled statuses
    if (['cancelled', 'canceled', 'cancel', 'aborted'].contains(statusLower)) {
      return 'cancelled';
    }

    // Pending statuses
    if (['pending', 'processing', 'initiated', 'in_progress']
        .contains(statusLower)) {
      return 'pending';
    }

    // Timeout/Unknown
    if (['timeout', 'expired'].contains(statusLower)) {
      return 'timeout';
    }

    debugPrint(
        '⚠️ RFID PAYU: Unknown status "$status", defaulting to "unknown"');
    return 'unknown';
  }

  /// Create standardized PayU payload for backend (matching wallet implementation)
  Map<String, dynamic> _createStandardizedPayUPayload(
      PayUPaymentResult result, String txnId, String normalizedStatus) {
    // Enhance response data with normalized status
    final enhancedResponse = Map<String, dynamic>.from(result.data ?? {});
    enhancedResponse['status'] = normalizedStatus;
    enhancedResponse['normalized_status'] = normalizedStatus;
    enhancedResponse['original_status'] = result.data?['status'];
    enhancedResponse['processed_at'] = DateTime.now().toIso8601String();
    enhancedResponse['result_type'] = result.type.toString();

    return {
      'status': normalizedStatus,
      'txnid': txnId,
      'hash': result.data?['hash'] ?? '',
      'response': enhancedResponse,
      // Additional metadata for backend processing
      'client_timestamp': DateTime.now().toIso8601String(),
      'client_version': 'flutter_rfid_v1.0',
      'payment_gateway': 'payu',
      'order_type': 'rfid_card',
    };
  }
}
