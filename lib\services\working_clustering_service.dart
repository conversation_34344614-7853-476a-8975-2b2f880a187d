import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

/// Working clustering service compatible with Google Maps Flutter 2.5.0
class WorkingClusteringService {
  static final WorkingClusteringService _instance =
      WorkingClusteringService._internal();
  factory WorkingClusteringService() => _instance;
  WorkingClusteringService._internal();

  // Enhanced cache for cluster icons with size limits
  final Map<int, BitmapDescriptor> _clusterIconCache = {};
  static const int _maxCacheSize = 50; // Limit cache size for memory efficiency

  // Performance optimization: Cache cluster results and markers with LRU eviction
  final Map<String, List<_Cluster>> _clusterCache = {};
  final Map<String, Set<Marker>> _markerCache = {};
  final Map<String, DateTime> _cacheAccessTimes =
      {}; // Track access times for LRU
  String? _lastCacheKey;

  // Performance monitoring
  int _cacheHits = 0;
  int _cacheMisses = 0;

  // Clustering parameters - now using viewport-based detection

  /// Generate clustered markers using real-time viewport-based overlap detection
  Future<Set<Marker>> generateClusteredMarkers({
    required List<Map<String, dynamic>> stations,
    required double zoomLevel,
    required Function(Map<String, dynamic>) onStationTap,
    required Function(List<Map<String, dynamic>>) onClusterTap,
    required Future<Marker?> Function(Map<String, dynamic>) createStationMarker,
    bool forceRefresh =
        false, // Force refresh to bypass cache for focus state changes
  }) async {
    if (stations.isEmpty) return {};

    // Create cache key for performance optimization
    final cacheKey = _generateCacheKey(stations, zoomLevel);

    // Check marker cache first for complete performance boost (unless force refresh)
    if (!forceRefresh && _markerCache.containsKey(cacheKey)) {
      _cacheHits++;
      _cacheAccessTimes[cacheKey] = DateTime.now(); // Update LRU
      debugPrint(
          '🚀 CACHE HIT: Using cached markers (hits: $_cacheHits, misses: $_cacheMisses)');
      return _markerCache[cacheKey]!;
    }

    if (forceRefresh) {
      debugPrint('🎯 FORCE REFRESH: Bypassing cache for focus state update');
      // Clear relevant caches to ensure fresh markers
      _markerCache.remove(cacheKey);
      _clusterCache.remove(cacheKey);
    }

    _cacheMisses++;
    debugPrint(
        '💾 CACHE MISS: Generating new markers (hits: $_cacheHits, misses: $_cacheMisses)');

    // CLUSTERING FIX: Clear old cache entries when zoom level changes significantly
    _clearOldZoomCaches(zoomLevel);

    // Use viewport-based clustering with cluster caching
    List<_Cluster> clusters;
    if (_clusterCache.containsKey(cacheKey)) {
      clusters = _clusterCache[cacheKey]!;
      _cacheAccessTimes[cacheKey] = DateTime.now(); // Update LRU
    } else {
      clusters = _createViewportBasedClusters(stations, zoomLevel);
      _manageCacheSize(); // Prevent memory bloat
      _clusterCache[cacheKey] = clusters;
      _cacheAccessTimes[cacheKey] = DateTime.now();
    }

    Set<Marker> markers;
    if (clusters.length == stations.length) {
      // No clustering needed - all markers are individual
      markers = await _createIndividualApiMarkers(
          stations, onStationTap, createStationMarker);
    } else {
      // Clustering needed - some markers overlap
      markers = await _createClusterMarkers(
          clusters, onStationTap, onClusterTap, createStationMarker);
    }

    // Cache the final markers for next time with LRU management
    _markerCache[cacheKey] = markers;
    _cacheAccessTimes[cacheKey] = DateTime.now();
    _lastCacheKey = cacheKey;

    return markers;
  }

  /// Create individual markers using the API marker creation function
  Future<Set<Marker>> _createIndividualApiMarkers(
    List<Map<String, dynamic>> stations,
    Function(Map<String, dynamic>) onStationTap,
    Future<Marker?> Function(Map<String, dynamic>) createStationMarker,
  ) async {
    final Set<Marker> markers = {};

    debugPrint('🎯 Creating ${stations.length} individual API markers');

    for (final station in stations) {
      try {
        // CLUSTERING FIX: Use the existing API marker creation function to preserve all functionality
        final marker = await createStationMarker(station);
        if (marker != null) {
          markers.add(marker);
          debugPrint('✅ CLUSTERING: Created individual API marker for station: ${station['id']}');
        } else {
          debugPrint('⚠️ CLUSTERING: API marker creation returned null for station: ${station['id']}');
          // Don't create fallback - let the API handle its own fallbacks
        }
      } catch (e) {
        debugPrint('❌ CLUSTERING: Error creating individual API marker for station ${station['id']}: $e');
        // Don't create fallback markers - let the API marker service handle fallbacks
      }
    }

    debugPrint(
        '🎯 WORKING CLUSTERING: Created ${markers.length} individual API markers');
    return markers;
  }

  /// Generate cache key for real-time viewport-based clustering
  String _generateCacheKey(
      List<Map<String, dynamic>> stations, double zoomLevel) {
    // Create a lightweight cache key based on station count, zoom, and first/last station IDs
    final stationCount = stations.length;
    // REAL-TIME CLUSTERING: Use very precise zoom rounding for immediate viewport response
    final zoomRounded = (zoomLevel * 10).round() / 10; // Round to nearest 0.1 for real-time sensitivity
    final firstId =
        stations.isNotEmpty ? stations.first['id']?.toString() ?? '' : '';
    final lastId =
        stations.isNotEmpty ? stations.last['id']?.toString() ?? '' : '';
    return '${stationCount}_${zoomRounded}_${firstId}_$lastId';
  }

  /// Manage cache size to prevent memory bloat using LRU eviction
  void _manageCacheSize() {
    const maxCacheEntries = _maxCacheSize;

    if (_clusterCache.length > maxCacheEntries ||
        _markerCache.length > maxCacheEntries) {
      // Sort cache entries by access time (LRU)
      final sortedEntries = _cacheAccessTimes.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));

      // Remove oldest entries until we're under the limit
      final entriesToRemove = sortedEntries.length -
          maxCacheEntries +
          10; // Remove extra for efficiency

      if (entriesToRemove > 0) {
        for (int i = 0; i < entriesToRemove && i < sortedEntries.length; i++) {
          final keyToRemove = sortedEntries[i].key;
          _clusterCache.remove(keyToRemove);
          _markerCache.remove(keyToRemove);
          _cacheAccessTimes.remove(keyToRemove);
        }
        debugPrint(
            '🧹 CACHE CLEANUP: Removed $entriesToRemove old entries via LRU');
      }
    }
  }

  /// Create clusters based on viewport overlap detection
  List<_Cluster> _createViewportBasedClusters(
      List<Map<String, dynamic>> stations, double zoomLevel) {
    if (stations.isEmpty) return [];

    // Performance optimization: Create cache key
    final cacheKey = '${stations.length}_${zoomLevel.toStringAsFixed(1)}';

    // Check cache first
    if (_lastCacheKey == cacheKey && _clusterCache.containsKey(cacheKey)) {
      debugPrint('🚀 PERFORMANCE: Using cached clusters for $cacheKey');
      return _clusterCache[cacheKey]!;
    }

    // Calculate screen overlap threshold based on zoom level
    final double overlapThresholdPixels = _getScreenOverlapThreshold(zoomLevel);

    debugPrint(
        '🔗 REAL-TIME VIEWPORT: Computing ${overlapThresholdPixels}px overlap threshold for zoom $zoomLevel');

    final List<_Cluster> clusters = [];
    final List<Map<String, dynamic>> unprocessed = List.from(stations);

    while (unprocessed.isNotEmpty) {
      final current = unprocessed.removeAt(0);
      final List<Map<String, dynamic>> clusterItems = [current];

      // Find overlapping stations based on screen coordinates
      final List<Map<String, dynamic>> toRemove = [];
      for (final other in unprocessed) {
        final screenDistance =
            _calculateScreenDistance(current, other, zoomLevel);

        if (screenDistance <= overlapThresholdPixels) {
          clusterItems.add(other);
          toRemove.add(other);
        }
      }

      // Remove clustered items from unprocessed list
      for (final item in toRemove) {
        unprocessed.remove(item);
      }

      // Calculate cluster center
      final center = _calculateClusterCenter(clusterItems);

      // Create cluster
      final cluster = _Cluster(
        items: clusterItems,
        center: center,
        id: 'viewport_cluster_${clusters.length}_${clusterItems.length}',
      );

      debugPrint('🔗 REAL-TIME VIEWPORT: Created cluster with ${cluster.count} stations');
      clusters.add(cluster);
    }

    debugPrint(
        '🔗 REAL-TIME VIEWPORT: Final result: ${clusters.length} clusters from ${stations.length} stations');

    // Performance optimization: Cache the result
    _clusterCache[cacheKey] = clusters;
    _lastCacheKey = cacheKey;

    // Limit cache size to prevent memory issues
    if (_clusterCache.length > 10) {
      final oldestKey = _clusterCache.keys.first;
      _clusterCache.remove(oldestKey);
    }

    return clusters;
  }

  /// Calculate screen distance between two stations in pixels
  double _calculateScreenDistance(Map<String, dynamic> station1,
      Map<String, dynamic> station2, double zoomLevel) {
    // Convert lat/lng to approximate screen coordinates
    final lat1 = (station1['latitude'] as num?)?.toDouble() ?? 0.0;
    final lng1 = (station1['longitude'] as num?)?.toDouble() ?? 0.0;
    final lat2 = (station2['latitude'] as num?)?.toDouble() ?? 0.0;
    final lng2 = (station2['longitude'] as num?)?.toDouble() ?? 0.0;

    // Approximate pixels per degree at different zoom levels
    // This is a simplified calculation - in reality it varies by latitude
    final pixelsPerDegree = _getPixelsPerDegree(zoomLevel);

    final deltaLat = (lat2 - lat1) * pixelsPerDegree;
    final deltaLng =
        (lng2 - lng1) * pixelsPerDegree * math.cos(lat1 * math.pi / 180);

    return math.sqrt(deltaLat * deltaLat + deltaLng * deltaLng);
  }

  /// Get pixels per degree for zoom level (approximate)
  double _getPixelsPerDegree(double zoomLevel) {
    // At zoom level 0, the world is 256 pixels wide
    // Each zoom level doubles the resolution
    final worldPixelWidth = 256 * math.pow(2, zoomLevel);
    return worldPixelWidth / 360; // 360 degrees around the world
  }

  /// Get screen overlap threshold in pixels based on zoom level
  double _getScreenOverlapThreshold(double zoomLevel) {
    // Marker size is approximately 40-60 pixels
    // We want to cluster when markers would overlap or be too close
    if (zoomLevel <= 5) {
      return 20.0; // Very close clustering for world view
    } else if (zoomLevel <= 8) {
      return 30.0; // Close clustering for country view
    } else if (zoomLevel <= 10) {
      return 40.0; // Moderate clustering for region view
    } else if (zoomLevel <= 12) {
      return 50.0; // Wider spacing for city view
    } else if (zoomLevel <= 14) {
      return 60.0; // Even wider spacing for district view
    } else {
      return 80.0; // Wide spacing for street view - less clustering
    }
  }

  /// Create markers for clusters
  Future<Set<Marker>> _createClusterMarkers(
    List<_Cluster> clusters,
    Function(Map<String, dynamic>) onStationTap,
    Function(List<Map<String, dynamic>>) onClusterTap,
    Future<Marker?> Function(Map<String, dynamic>) createStationMarker,
  ) async {
    final Set<Marker> markers = {};

    for (final cluster in clusters) {
      if (cluster.isMultiple) {
        // Create cluster marker
        debugPrint('🔗 Creating cluster marker with ${cluster.count} stations');
        final marker = Marker(
          markerId: MarkerId(cluster.id),
          position: cluster.center,
          icon: await _getClusterIcon(cluster.count),
          onTap: () => onClusterTap(cluster.items),
          anchor: const Offset(0.5, 0.5),
          zIndexInt: 1000,
        );
        markers.add(marker);
      } else {
        // Create individual API marker for single-item cluster
        final station = cluster.items.first;
        debugPrint(
            '🎯 Creating individual API marker for single station: ${station['id']}');

        try {
          // CLUSTERING FIX: Use the existing API marker creation function with proper error handling
          final apiMarker = await createStationMarker(station);
          if (apiMarker != null) {
            markers.add(apiMarker);
            debugPrint(
                '✅ CLUSTERING: Created API marker for single station: ${station['id']}');
          } else {
            debugPrint('⚠️ CLUSTERING: API marker creation returned null for station: ${station['id']}');
            // Don't use fallback - let the API handle its own fallbacks
            // This ensures proper API icons are always used
          }
        } catch (e) {
          debugPrint(
              '❌ CLUSTERING: Error creating API marker for station ${station['id']}: $e');
          // Don't create fallback markers - let the API marker service handle fallbacks
          // This ensures consistent marker appearance
        }
      }
    }

    debugPrint(
        '🔗 WORKING CLUSTERING: Created ${markers.length} total markers from ${clusters.length} clusters');
    return markers;
  }

  /// Create fallback marker when API marker creation fails
  Future<Marker> _createFallbackMarker(
    Map<String, dynamic> station,
    Function(Map<String, dynamic>) onStationTap,
  ) async {
    return Marker(
      markerId: MarkerId(station['id']?.toString() ?? ''),
      position: LatLng(
        (station['latitude'] as num?)?.toDouble() ?? 0.0,
        (station['longitude'] as num?)?.toDouble() ?? 0.0,
      ),
      icon: await _getStationIcon(station['status']?.toString() ?? 'Available'),
      infoWindow: InfoWindow(
        title: station['name']?.toString() ?? 'Station',
        snippet:
            '${station['distance']?.toString() ?? '0.0'} km • ${station['status']?.toString() ?? 'Unknown'}',
      ),
      onTap: () => onStationTap(station),
    );
  }

  /// Calculate distance between two points in kilometers
  double _calculateDistance(
      double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Earth's radius in kilometers
    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);
    final double a = math.sin(dLat / 2) * math.sin(dLat / 2) +
        math.cos(_degreesToRadians(lat1)) *
            math.cos(_degreesToRadians(lat2)) *
            math.sin(dLon / 2) *
            math.sin(dLon / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));
    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (math.pi / 180);
  }

  /// Calculate cluster center from items
  LatLng _calculateClusterCenter(List<Map<String, dynamic>> items) {
    double totalLat = 0.0;
    double totalLng = 0.0;

    for (final item in items) {
      totalLat += (item['latitude'] as num?)?.toDouble() ?? 0.0;
      totalLng += (item['longitude'] as num?)?.toDouble() ?? 0.0;
    }

    return LatLng(totalLat / items.length, totalLng / items.length);
  }

  /// Get clustering distance based on zoom level (optimized for India full map view)
  double _getClusteringDistance(double zoomLevel) {
    if (zoomLevel <= 4) {
      return 500.0; // Extremely aggressive clustering for world view
    } else if (zoomLevel <= 5) {
      return 300.0; // Very aggressive clustering for continent view
    } else if (zoomLevel <= 6) {
      return 200.0; // Aggressive clustering for India full map view
    } else if (zoomLevel <= 7) {
      return 150.0; // Strong clustering for large country view
    } else if (zoomLevel <= 8) {
      return 100.0; // Moderate clustering for country regions
    } else if (zoomLevel <= 10) {
      return 50.0; // Conservative clustering for state view
    } else if (zoomLevel <= 12) {
      return 20.0; // Minimal clustering for city view
    } else if (zoomLevel <= 13) {
      return 8.0; // Very minimal clustering for district view
    } else {
      return 3.0; // Tiny clustering just before individual markers
    }
  }

  /// Get cluster icon with count (using primary green lime color) - optimized with caching
  Future<BitmapDescriptor> _getClusterIcon(int count) async {
    // Check cache first for performance
    if (_clusterIconCache.containsKey(count)) {
      return _clusterIconCache[count]!;
    }

    // Manage cache size to prevent memory issues
    if (_clusterIconCache.length >= _maxCacheSize) {
      _clusterIconCache.clear(); // Simple cache clearing strategy
    }

    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);
    const double size = 60.0; // Compact appearance
    const Offset center = Offset(size / 2, size / 2);
    const double radius =
        size / 2 - 8; // Reduced inner circle, more space for border

    // Draw shadow for depth effect
    final Paint shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(center.translate(1, 1), radius, shadowPaint);

    // Create pulse border padding effect with filled circles
    final Paint pulseOuter = Paint()
      ..color =
          const Color(0xFF8cc051).withValues(alpha: 0.15) // Outermost pulse
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    final Paint pulseMid = Paint()
      ..color = const Color(0xFF8cc051).withValues(alpha: 0.25) // Middle pulse
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    final Paint pulseInner = Paint()
      ..color = const Color(0xFF8cc051).withValues(alpha: 0.35) // Inner pulse
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    // Draw main cluster circle with primary green lime color
    final Paint fillPaint = Paint()
      ..color = const Color(0xFF8cc051).withValues(alpha: 0.95) // Main circle
      ..style = PaintingStyle.fill
      ..isAntiAlias = true; // Smooth edges

    // Draw pulse border padding (largest to smallest for layered effect)
    canvas.drawCircle(center, size / 2 - 1.5, pulseOuter); // Outermost border
    canvas.drawCircle(center, size / 2 - 3.0, pulseMid); // Middle border
    canvas.drawCircle(center, size / 2 - 4.5, pulseInner); // Inner border

    // Draw the main filled circle on top (smallest)
    canvas.drawCircle(center, radius, fillPaint);

    // Draw count text
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: count.toString(),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16, // Reduced from 18 for smaller circle
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.dx - textPainter.width / 2,
        center.dy - textPainter.height / 2,
      ),
    );

    // Convert to image
    final ui.Picture picture = pictureRecorder.endRecording();
    final ui.Image image = await picture.toImage(size.toInt(), size.toInt());
    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List uint8List = byteData!.buffer.asUint8List();

    final BitmapDescriptor descriptor = BitmapDescriptor.bytes(uint8List);

    // Cache the icon
    _clusterIconCache[count] = descriptor;

    return descriptor;
  }

  /// Get station icon based on status
  Future<BitmapDescriptor> _getStationIcon(String status) async {
    Color markerColor;
    switch (status.toLowerCase()) {
      case 'available':
        markerColor = const Color(0xFF8cc051); // Primary green lime color
        break;
      case 'in use':
      case 'charging':
        markerColor = Colors.orange;
        break;
      case 'unavailable':
        markerColor = Colors.red;
        break;
      default:
        markerColor = const Color(0xFF8cc051);
    }

    return await _createCustomIcon(markerColor, size: 40.0);
  }

  /// Create custom marker icon
  Future<BitmapDescriptor> _createCustomIcon(Color color,
      {double size = 40.0}) async {
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);

    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Draw marker circle
    const Offset center = Offset(20, 20);
    const double radius = 15;

    canvas.drawCircle(center, radius, paint);

    // Convert to image
    final ui.Picture picture = pictureRecorder.endRecording();
    final ui.Image image = await picture.toImage(size.toInt(), size.toInt());
    final ByteData? byteData =
        await image.toByteData(format: ui.ImageByteFormat.png);
    final Uint8List uint8List = byteData!.buffer.asUint8List();

    return BitmapDescriptor.bytes(uint8List);
  }

  /// CLUSTERING FIX: Clear old zoom-level caches when zoom changes significantly
  void _clearOldZoomCaches(double currentZoomLevel) {
    final keysToRemove = <String>[];

    for (final key in _markerCache.keys) {
      final parts = key.split('_');
      if (parts.length >= 2) {
        final cachedZoom = double.tryParse(parts[1]) ?? 0.0;
        // Remove cache entries that are more than 2 zoom levels away
        if ((cachedZoom - currentZoomLevel).abs() > 2.0) {
          keysToRemove.add(key);
        }
      }
    }

    for (final key in keysToRemove) {
      _markerCache.remove(key);
      _clusterCache.remove(key);
      _cacheAccessTimes.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      debugPrint('🧹 CLUSTERING: Cleared ${keysToRemove.length} old zoom cache entries');
    }
  }

  /// Clear cache
  void clearCache() {
    _clusterIconCache.clear();
    _markerCache.clear();
    _clusterCache.clear();
    _cacheAccessTimes.clear();
    debugPrint('🎯 CLUSTERING: Service cache cleared');
  }
}

/// Simple cluster data structure
class _Cluster {
  final List<Map<String, dynamic>> items;
  final LatLng center;
  final String id;

  _Cluster({
    required this.items,
    required this.center,
    required this.id,
  });

  bool get isMultiple => items.length > 1;
  int get count => items.length;
}
